{"name": "vue-antd-jeecg", "version": "2.4.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:site": "vue-cli-service build --mode site", "build:test": "vue-cli-service build --mode test", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/data-set": "^0.11.4", "@fullcalendar/core": "^4.4.0", "@fullcalendar/daygrid": "^4.4.0", "@fullcalendar/interaction": "^4.4.0", "@fullcalendar/resource-timeline": "^4.4.0", "@fullcalendar/timegrid": "^4.4.0", "@fullcalendar/vue": "^4.4.0", "@riophae/vue-treeselect": "^0.4.0", "@supermap/iclient-leaflet": "^11.0.0", "@tinymce/tinymce-vue": "^2.1.0", "@toast-ui/editor": "^2.1.2", "ant-design-vue": "^1.7.8", "area-data": "^5.0.6", "axios": "^0.21.1", "bpmn-js": "^11.1.0", "bpmn-js-bpmnlint": "^0.21.0", "bpmnlint": "^9.2.0", "clipboard": "^2.0.4", "codemirror": "^5.62.3", "dayjs": "^1.8.0", "diagram-js": "^11.4.1", "docx-preview": "0.1.11", "dom-align": "1.12.0", "echarts": "^5.1.2", "element-ui": "^2.15.14", "enquire.js": "^2.1.6", "file-saver": "^2.0.5", "fuse.js": "^6.6.2", "highlight.js": "9.18.5", "js-cookie": "^2.2.0", "leaflet": "^1.8.0", "leaflet.markercluster": "^1.5.3", "leaflet.wmts": "^1.0.2", "lodash": "^4.17.21", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "moment": "^2.29.3", "nprogress": "^0.2.0", "ol": "^4.6.5", "pdfjs-dist": "^2.16.105", "qrcodejs2": "^0.0.2", "quill": "^1.3.7", "screenfull": "^5.1.0", "tinymce": "^5.9.1", "viser-vue": "^2.4.8", "vkbeautify": "^0.99.3", "vue": "^2.7.16", "vue-area-linkage": "^5.1.0", "vue-count-to": "^1.0.13", "vue-infinite-scroll": "^2.0.2", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-lunar-full-calendar": "^1.2.7", "vue-photo-preview": "^1.1.3", "vue-pop-colorpicker": "^1.0.2", "vue-print-nb-jeecg": "^1.0.9", "vue-router": "^3.0.1", "vuedraggable": "^2.20.0", "vuex": "^3.1.0", "vxe-table": "2.9.13", "vxe-table-plugin-antd": "1.8.10", "xcrud": "^0.4.19", "xe-utils": "2.4.8", "xlsx": "0.17.0", "xlsx-style": "0.8.13"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/eslint-parser": "^7.28.0", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "compression-webpack-plugin": "^6.1.1", "eslint": "^7.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^7.20.0", "less": "^4.1.3", "less-loader": "^10.2.0", "path-browserify": "^1.0.1", "prettier": "^3.6.2", "sass": "^1.66.1", "sass-loader": "^10.1.1", "script-loader": "^0.7.2", "vue-svg-icon": "^1.2.9", "vue-template-compiler": "^2.7.16"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}