module.exports = {
  root: true,
  env: {
    browser: true,
    node: true
  },
  extends: ['plugin:vue/vue2-recommended', 'eslint:recommended', 'plugin:prettier/recommended'],
  // 指定 ESLint 的解析器
  parserOptions: {
    // 使用 @babel/eslint-parser，以支持所有最新的 ECMAScript 语法
    // 这个解析器能够很好地与 Babel 配置配合
    parser: '@babel/eslint-parser',
    requireConfigFile: false, // 允许解析器在没有 babel.config.js 文件的情况下工作
    ecmaVersion: 'latest', // 支持最新的 ECMAScript 版本
    sourceType: 'module' // 使用 ES 模块
  },
  rulers: {
    // ESLint 核心规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',

    // Vue 相关规则 (根据你的偏好选择)
    'vue/no-v-html': 'off', // 允许使用 v-html，根据项目安全需求决定是否开启
    'vue/multi-word-component-names': 'off', // 关闭组件名必须是多词的要求，方便创建如 App.vue, Index.vue 等组件
    'vue/html-self-closing': [
      'warn',
      {
        html: {
          void: 'always',
          normal: 'never',
          component: 'always'
        },
        svg: 'always',
        math: 'always'
      }
    ]
  }
}
