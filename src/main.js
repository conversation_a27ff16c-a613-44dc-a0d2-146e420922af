/** init domain config */
import './config'

import Vue from 'vue'
import App from './App.vue'
import Storage from 'vue-ls'
import router from './router'
import store from './store/'
import { VueAxios } from '@/utils/request'

// ruoyi引用element
import Element from 'element-ui'
import './assets/styles/element-variables.scss'
// ruoyi表单配置图标
import './assets/icons' // icon

// 引入全局样式进行覆盖
import './styles/base.less'
// 定义全局变量
Vue.prototype.$showFlag = false
Vue.prototype.$eventBus = new Vue()

import Antd, { version } from 'ant-design-vue'
console.log('ant-design-vue version:', version)

import Viser from 'viser-vue'
import 'ant-design-vue/dist/antd.less' // or 'ant-design-vue/dist/antd.less'

// import ElementUI from 'element-ui'
// import 'element-ui/lib/theme-chalk/index.css'

import '@/permission' // permission control
import '@/utils/filter' // base filter
import Print from 'vue-print-nb-jeecg'
import preview from 'vue-photo-preview'
import 'vue-photo-preview/dist/skin.css'
import SSO from '@/cas/sso.js'
import {
  ACCESS_TOKEN,
  DEFAULT_COLOR,
  DEFAULT_THEME,
  DEFAULT_LAYOUT_MODE,
  DEFAULT_COLOR_WEAK,
  SIDEBAR_TYPE,
  DEFAULT_FIXED_HEADER,
  DEFAULT_FIXED_HEADER_HIDDEN,
  DEFAULT_FIXED_SIDEMENU,
  DEFAULT_CONTENT_WIDTH_TYPE,
  DEFAULT_MULTI_PAGE,
} from '@/store/mutation-types'
import config from '@/defaultSettings'
import Cookies from 'js-cookie'

import JDictSelectTag from './components/dict/index.js'
import hasPermission from '@/utils/hasPermission'
import DynamicForm from '@/components/online/autoform/index'
import vueBus from '@/utils/vueBus'
import JeecgComponents from '@/components/jeecg/index'
import '@/assets/less/JAreaLinkage.less'
import VueAreaLinkage from 'vue-area-linkage'
import * as WPS from '@/components/wps/util/jwps.es6'
import '@/components/jeecg/JVxeTable/install'
import '@/components/JVxeCells/install'
import * as L from 'leaflet'
import 'leaflet.wmts'
import '@supermap/iclient-leaflet'
import 'leaflet/dist/leaflet.css'
// ruoyi
import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

import './assets/icons' // icon
import Tinymce from '@/components/tinymce/index.vue'
import { getDicts } from '@/api/system/dict/data'
import { getConfigKey } from '@/api/system/config'
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree,
} from '@/utils/ruoyi'

// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 富文本组件
import Editor from '@/components/Editor'
// 文件上传组件
import FileUpload from '@/components/FileUpload'
// 图片上传组件
import ImageUpload from '@/components/ImageUpload'
// 图片预览组件
import ImagePreview from '@/components/ImagePreview'
// 字典标签组件
import DictTag from '@/components/DictTag'
// 字典数据组件
import DictData from '@/components/DictData'

Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
// new Vue({
//   el: '#app',
//   router,
//   store,
//   render: h => h(App)
// })
Vue.use(directive)
Vue.use(plugins)
// 全局组件挂载
Vue.component('tinymce', Tinymce)
// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)
DictData.install()

Vue.prototype.wps = WPS
Vue.config.productionTip = false
Vue.L = Vue.prototype.$L = L
Vue.use(Storage, config.storageOptions)
Vue.use(Antd)
Vue.use(VueAxios, router)
Vue.use(Viser)
Vue.use(DynamicForm)
Vue.use(hasPermission)
Vue.use(JDictSelectTag)
Vue.use(Print)
Vue.use(preview)
Vue.use(vueBus)
Vue.use(JeecgComponents)
Vue.use(VueAreaLinkage)

import CustomRequire from '@/views/itemList/components/CustomRequire.vue'
Vue.component('CustomRequire', CustomRequire)

import CustomToolTip from '@/views/itemList/modules/customToolTip'
Vue.component('CustomToolTip', CustomToolTip)
import { checkNull } from '@/utils/util'
Vue.prototype.checkNull = checkNull

// 处理ElementUI与Ant弹窗一致
import { Modal } from 'ant-design-vue'
function confirmElement(
  content,
  buttonObj = {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }
) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: '提示',
      content: content,
      okText: buttonObj.confirmButtonText,
      cancelText: buttonObj.cancelButtonText,
      onOk: () => {
        resolve()
      },
      onCancel: () => {
        console.log('error')
        reject('error')
      },
    })
  })
}
Vue.prototype.$confirmElement = confirmElement
Vue.prototype.$confirmAnt = Modal.confirm

// Vue.use(ElementUI)
// ruoyi引用element
Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
})

SSO.init(() => {
  main()
})
function main() {
  new Vue({
    router,
    store,
    mounted() {
      // 将子系统数组存储到本地存储插件
      Vue.ls.set('systemName', config.systemName)
      store.commit('SET_SIDEBAR_TYPE', Vue.ls.get(SIDEBAR_TYPE, true))
      store.commit('TOGGLE_THEME', Vue.ls.get(DEFAULT_THEME, config.navTheme))
      store.commit('TOGGLE_LAYOUT_MODE', Vue.ls.get(DEFAULT_LAYOUT_MODE, config.layout))
      store.commit('TOGGLE_FIXED_HEADER', Vue.ls.get(DEFAULT_FIXED_HEADER, config.fixedHeader))
      store.commit('TOGGLE_FIXED_SIDERBAR', Vue.ls.get(DEFAULT_FIXED_SIDEMENU, config.fixSiderbar))
      store.commit(
        'TOGGLE_CONTENT_WIDTH',
        Vue.ls.get(DEFAULT_CONTENT_WIDTH_TYPE, config.contentWidth)
      )
      store.commit(
        'TOGGLE_FIXED_HEADER_HIDDEN',
        Vue.ls.get(DEFAULT_FIXED_HEADER_HIDDEN, config.autoHideHeader)
      )
      store.commit('TOGGLE_WEAK', Vue.ls.get(DEFAULT_COLOR_WEAK, config.colorWeak))
      store.commit('TOGGLE_COLOR', Vue.ls.get(DEFAULT_COLOR, config.primaryColor))
      store.commit('SET_TOKEN', Vue.ls.get(ACCESS_TOKEN))
      store.commit('SET_MULTI_PAGE', Vue.ls.get(DEFAULT_MULTI_PAGE, config.multipage))
    },
    render: h => h(App),
  }).$mount('#app')
}
